# Gemini CLI 实际提示词系统分析

## 📋 概述

这个文档详细分析 Gemini CLI 项目中实际使用的提示词系统，而不是理论设计。通过分析源代码，我们可以看到 Google 是如何设计和实现 AI CLI 工具的提示词工程的。

## 🎯 核心系统提示词

### 1. 主系统提示词结构

Gemini CLI 的核心系统提示词位于 `packages/core/src/core/prompts.ts` 文件中，采用模块化设计：

```typescript
export function getCoreSystemPrompt(userMemory?: string): string {
  // 支持自定义系统提示词覆盖
  const systemMdEnabled = checkSystemMdOverride();
  
  const basePrompt = systemMdEnabled 
    ? fs.readFileSync(systemMdPath, 'utf8')  // 从文件加载
    : getBuiltInSystemPrompt();              // 使用内置提示词
    
  // 添加用户记忆
  const memorySuffix = userMemory ? `\n\n---\n\n${userMemory.trim()}` : '';
  
  return `${basePrompt}${memorySuffix}`;
}
```

### 2. 实际的核心提示词内容

#### A. 身份定义
```
You are an interactive CLI agent specializing in software engineering tasks. 
Your primary goal is to help users safely and efficiently, adhering strictly 
to the following instructions and utilizing your available tools.
```

**设计理念**：
- 明确定义为"CLI agent"而不是通用助手
- 专注于"software engineering tasks"
- 强调"safely and efficiently"

#### B. 核心原则 (Core Mandates)
```
- **Conventions:** Rigorously adhere to existing project conventions
- **Libraries/Frameworks:** NEVER assume availability, verify first
- **Style & Structure:** Mimic existing code style and patterns
- **Idiomatic Changes:** Ensure changes integrate naturally
- **Comments:** Add sparingly, focus on 'why' not 'what'
- **Proactiveness:** Include reasonable follow-up actions
- **Confirm Ambiguity:** Don't exceed scope without confirmation
- **Path Construction:** Always use absolute paths for file operations
```

**关键洞察**：
- 强调遵循项目现有约定，而不是强加外部标准
- 明确禁止假设依赖库的存在
- 要求验证后再使用
- 注重代码风格的一致性

### 3. 工作流程设计

#### A. 软件工程任务流程
```
1. **Understand:** Use grep/glob tools to understand context
2. **Plan:** Build coherent plan based on understanding
3. **Implement:** Use tools while adhering to conventions
4. **Verify (Tests):** Run project-specific test commands
5. **Verify (Standards):** Execute build/lint/type-check commands
```

#### B. 新应用开发流程
```
1. **Understand Requirements:** Analyze user request
2. **Propose Plan:** Present clear, high-level summary
3. **User Approval:** Obtain approval for proposed plan
4. **Implementation:** Autonomously implement features
5. **Verify:** Review against original request
6. **Solicit Feedback:** Provide startup instructions
```

**技术栈偏好**：
- **Web Frontend**: React + Bootstrap + Material Design
- **Backend**: Node.js + Express.js 或 Python + FastAPI
- **Full-stack**: Next.js 或 Django/Flask + React
- **CLI**: Python 或 Go
- **Mobile**: Compose Multiplatform 或 Flutter
- **Games**: Three.js (3D) 或 HTML/CSS/JS (2D)

## 🎨 交互风格设计

### 1. 语调和风格 (Tone and Style)
```
- **Concise & Direct:** Professional, direct, concise tone
- **Minimal Output:** Aim for fewer than 3 lines per response
- **Clarity over Brevity:** Prioritize clarity for essential explanations
- **No Chitchat:** Avoid conversational filler or preambles
- **Formatting:** Use GitHub-flavored Markdown
- **Tools vs. Text:** Use tools for actions, text only for communication
```

**实际示例**：
```
user: 1 + 2
model: 3

user: is 13 a prime number?
model: true

user: list files here.
model: [tool_call: ls for path '/path/to/project']
```

### 2. 安全和安全规则
```
- **Explain Critical Commands:** Must explain commands that modify system
- **Security First:** Never expose secrets or sensitive information
```

## 🛠️ 工具使用指导

### 1. 文件操作规则
```
- **File Paths:** Always use absolute paths
- **Parallelism:** Execute independent tool calls in parallel
- **Command Execution:** Use ShellTool with safety explanations
- **Background Processes:** Use & for long-running commands
- **Interactive Commands:** Avoid commands requiring user interaction
```

### 2. 记忆系统使用
```
- **User-Related Facts:** Remember user preferences and coding styles
- **Session Persistence:** Information that helps future interactions
- **NOT for:** General project context or temporary information
- **Confirmation:** Ask "Should I remember that for you?" when unsure
```

## 🔧 动态提示词组件

### 1. 沙箱环境检测
```typescript
// 根据环境变量动态添加沙箱相关指导
if (isSandboxExec) {
  return `# macOS Seatbelt
You are running under macos seatbelt with limited access...`;
} else if (isGenericSandbox) {
  return `# Sandbox
You are running in a sandbox container...`;
} else {
  return `# Outside of Sandbox
You are running directly on the user's system...`;
}
```

### 2. Git 仓库检测
```typescript
// 如果在 Git 仓库中，添加 Git 相关指导
if (isGitRepository(process.cwd())) {
  return `# Git Repository
- Always start by gathering information using shell commands
- git status, git diff HEAD, git log -n 3
- Always propose a draft commit message
- Never push without explicit user request`;
}
```

## 📚 实际示例分析

### 1. 代码重构示例
```
user: Refactor the auth logic in src/auth.py to use requests instead of urllib.

model: Okay, I can refactor 'src/auth.py'.
First, I'll analyze the code and check for a test safety net.
[tool_call: glob for 'tests/test_auth.py']
[tool_call: read_file for '/path/to/tests/test_auth.py']

Great, tests exist. I'll confirm 'requests' is a dependency.
[tool_call: read_file for '/path/to/requirements.txt']

Here's the plan:
1. Replace urllib calls with requests
2. Add proper error handling
3. Remove old urllib import
4. Run linter and tests

Should I proceed?
```

**关键特点**：
- 先检查测试覆盖
- 验证依赖可用性
- 提供清晰的计划
- 征求用户确认

### 2. 文件搜索示例
```
user: Where are all the 'app.config' files?

model: [tool_call: glob for '**/app.config']

I found:
- /path/to/moduleA/app.config
- /path/to/moduleB/app.config

Which one would you like to start with, or should I read all?
```

**特点**：
- 直接使用工具
- 简洁的结果展示
- 提供后续选择

## 🔄 历史压缩提示词

### 1. 压缩系统设计
```typescript
export function getCompressionPrompt(): string {
  return `
You are the component that summarizes internal chat history into a given structure.

<state_snapshot>
    <overall_goal>
        <!-- User's high-level objective -->
    </overall_goal>
    
    <key_knowledge>
        <!-- Crucial facts and constraints -->
    </key_knowledge>
    
    <current_plan>
        <!-- Step-by-step plan with status -->
    </current_plan>
</state_snapshot>
  `;
}
```

**设计目的**：
- 保持长对话的上下文连续性
- 结构化存储关键信息
- 支持会话恢复

## 💡 提示词工程洞察

### 1. Google 的设计哲学

#### A. 实用主义优先
- 不追求完美的通用性，专注解决具体问题
- 强调与现有项目约定的兼容性
- 优先考虑开发者的实际工作流程

#### B. 安全第一
- 所有危险操作都要求解释
- 明确的权限和确认机制
- 沙箱环境的动态适配

#### C. 渐进式增强
- 基础功能简洁明了
- 高级功能通过动态组件添加
- 支持用户自定义覆盖

### 2. 与理论设计的对比

| 方面 | 理论设计 | Google 实际实现 |
|------|----------|----------------|
| 复杂度 | 复杂的多层提示词 | 简洁的单一提示词 |
| 个性化 | 复杂的用户画像 | 简单的记忆系统 |
| 场景化 | 多种场景模板 | 统一的工作流程 |
| 安全性 | 复杂的权限系统 | 简单的确认机制 |
| 扩展性 | 插件化提示词 | 动态组件注入 |

### 3. 关键成功因素

#### A. 简洁性
- 核心提示词只有约 300 行
- 避免过度复杂的指令
- 重点突出，易于理解

#### B. 实用性
- 基于真实开发场景设计
- 强调工具使用而非纯文本交互
- 注重结果验证

#### C. 一致性
- 统一的交互风格
- 一致的安全标准
- 可预测的行为模式

## 🚀 实施建议

### 1. 学习要点
- **从简单开始**：不要一开始就设计复杂的提示词系统
- **重视约定**：强调遵循现有项目约定而非强加标准
- **工具优先**：设计以工具调用为核心的交互模式
- **安全意识**：所有系统级操作都要有安全检查

### 2. 可复用的模式
- **动态组件注入**：根据环境动态添加相关指导
- **结构化示例**：使用具体示例展示期望的交互模式
- **分层验证**：测试 → 标准检查 → 用户确认
- **记忆系统**：简单但有效的上下文保持机制

这个分析展示了 Google 在 Gemini CLI 中实际采用的提示词工程方法，相比理论设计更加务实和高效。

## 🔍 具体工具调用提示词

### 1. 文件操作相关提示词

#### A. 路径构建要求
```
Before using any file system tool (e.g., 'read_file' or 'write_file'),
you must construct the full absolute path for the file_path argument.
Always combine the absolute path of the project's root directory with
the file's path relative to the root.

Example: if project root is /path/to/project/ and file is foo/bar/baz.txt,
the final path must be /path/to/project/foo/bar/baz.txt
```

#### B. 并行执行指导
```
Execute multiple independent tool calls in parallel when feasible
(i.e. searching the codebase).
```

**实际应用示例**：
```typescript
// ✅ 并行执行独立的搜索操作
[tool_call: grep for 'UserProfile']
[tool_call: glob for '**/*.test.ts']
[tool_call: read_file for '/path/to/package.json']

// ❌ 串行执行独立操作（效率低）
[tool_call: grep for 'UserProfile']
// 等待结果...
[tool_call: glob for '**/*.test.ts']
// 等待结果...
```

### 2. Shell 命令执行提示词

#### A. 安全解释要求
```
Before executing commands with 'shell' that modify the file system,
codebase, or system state, you *must* provide a brief explanation
of the command's purpose and potential impact.
```

#### B. 后台进程处理
```
Use background processes (via `&`) for commands that are unlikely
to stop on their own, e.g. `node server.js &`. If unsure, ask the user.
```

#### C. 交互式命令避免
```
Try to avoid shell commands that are likely to require user interaction
(e.g. `git rebase -i`). Use non-interactive versions when available
(e.g. `npm init -y` instead of `npm init`).
```

**实际示例**：
```
user: start the server
model: I'll run `node server.js &` in the background since servers
typically run continuously.
[tool_call: shell for 'node server.js &']

user: delete temp files
model: I'll run `rm -rf temp/`. This will permanently delete the temp
directory and all its contents.
[tool_call: shell for 'rm -rf /path/to/project/temp']
```

### 3. 记忆工具使用指导

#### A. 适用场景
```
Use the 'memory' tool to remember specific, *user-related* facts or
preferences when the user explicitly asks, or when they state a clear,
concise piece of information that would help personalize or streamline
*your future interactions with them*.

Examples:
- Preferred coding style
- Common project paths they use
- Personal tool aliases
```

#### B. 不适用场景
```
Do *not* use it for:
- General project context
- Temporary information
- Information that changes frequently
```

#### C. 确认机制
```
If unsure whether to save something, ask the user:
"Should I remember that for you?"
```

## 📋 实际的用户交互模式

### 1. 极简响应模式

#### A. 数学计算
```
user: 1 + 2
model: 3

user: is 13 a prime number?
model: true
```

#### B. 简单查询
```
user: list files here
model: [tool_call: ls for path '/path/to/project']

user: what's in package.json?
model: [tool_call: read_file for '/path/to/project/package.json']
```

### 2. 复杂任务的结构化响应

#### A. 代码分析任务
```
user: How do I update user profile in this system?

model: I'll search for profile-related code to understand the system.
[tool_call: grep for 'UserProfile|updateProfile|editUser']
[tool_call: glob for '**/profile/**']

(After analysis)
The `updateUserProfile` method in `UserProfileService.java` handles this.
It expects a user ID and `UserProfileDTO` object...
```

#### B. 重构任务
```
user: Refactor auth.py to use requests instead of urllib

model: I'll analyze the code and check for tests first.
[tool_call: read_file for '/path/to/src/auth.py']
[tool_call: glob for 'tests/*auth*']

(After analysis)
Good, tests exist. I'll verify requests is available.
[tool_call: read_file for '/path/to/requirements.txt']

Plan:
1. Replace urllib calls with requests
2. Add proper error handling
3. Remove old imports
4. Run tests

Should I proceed?
```

### 3. 错误处理和用户确认

#### A. 工具调用被取消
```
# 当用户取消工具调用时的处理
If a user cancels a function call, respect their choice and do _not_
try to make the function call again. Consider inquiring if they prefer
any alternative paths forward.
```

#### B. 无法完成请求
```
If unable/unwilling to fulfill a request, state so briefly (1-2 sentences)
without excessive justification. Offer alternatives if appropriate.
```

## 🎯 提示词的实际效果

### 1. 成功的设计元素

#### A. 明确的角色定位
- "CLI agent specializing in software engineering"
- 避免了通用助手的模糊性
- 用户期望更加明确

#### B. 具体的行为指导
- "fewer than 3 lines of text output per response"
- "always use absolute paths"
- "explain critical commands first"

#### C. 实用的工作流程
- 理解 → 计划 → 实施 → 验证
- 每个步骤都有具体的工具和方法

### 2. 避免的常见陷阱

#### A. 过度冗长
```
❌ 避免: "I understand you want me to help you with your request.
Let me analyze what you're asking for and then I'll provide a
comprehensive solution..."

✅ 实际: "I'll search for profile-related code."
[tool_call: grep for 'profile']
```

#### B. 假设依赖
```
❌ 避免: 直接使用 requests 库
✅ 实际: 先检查 requirements.txt 确认依赖存在
```

#### C. 忽略项目约定
```
❌ 避免: 使用标准的代码风格
✅ 实际: "Mimic the style, structure, and patterns of existing code"
```

## 🔧 自定义和扩展机制

### 1. 系统提示词覆盖

#### A. 环境变量控制
```typescript
// 支持用户自定义系统提示词
const systemMdVar = process.env.GEMINI_SYSTEM_MD;
if (systemMdVar && !['0', 'false'].includes(systemMdVar.toLowerCase())) {
  // 使用自定义提示词文件
  systemMdPath = resolveCustomPath(systemMdVar);
}
```

#### B. 文件路径支持
```bash
# 使用默认路径
export GEMINI_SYSTEM_MD=true  # 使用 .gemini/system.md

# 使用自定义路径
export GEMINI_SYSTEM_MD="~/my-custom-prompt.md"

# 禁用覆盖
export GEMINI_SYSTEM_MD=false
```

### 2. 提示词写入功能
```typescript
// 支持将内置提示词写入文件进行自定义
const writeSystemMdVar = process.env.GEMINI_WRITE_SYSTEM_MD;
if (writeSystemMdVar) {
  const customPath = resolveWritePath(writeSystemMdVar);
  fs.writeFileSync(customPath, basePrompt);
}
```

### 3. 记忆系统集成
```typescript
// 用户记忆会自动附加到系统提示词
const memorySuffix = userMemory && userMemory.trim().length > 0
  ? `\n\n---\n\n${userMemory.trim()}`
  : '';

return `${basePrompt}${memorySuffix}`;
```

## 📊 提示词效果测试

### 1. 快照测试
```typescript
// packages/core/src/core/prompts.test.ts
it('should return the base prompt when no userMemory is provided', () => {
  const prompt = getCoreSystemPrompt();
  expect(prompt).toContain('You are an interactive CLI agent');
  expect(prompt).toMatchSnapshot(); // 确保提示词结构稳定
});
```

### 2. 记忆集成测试
```typescript
it('should append userMemory with separator when provided', () => {
  const userMemory = 'User prefers TypeScript over JavaScript';
  const prompt = getCoreSystemPrompt(userMemory);
  expect(prompt).toContain('---\n\nUser prefers TypeScript');
});
```

## 💡 关键学习点

### 1. 简洁性胜过复杂性
- Google 选择了简单直接的提示词设计
- 避免了过度工程化的多层提示词系统
- 重点关注核心功能而非边缘情况

### 2. 实用性胜过理论完美
- 基于真实开发场景设计
- 强调与现有工具和流程的集成
- 优先解决实际问题而非追求理论完美

### 3. 安全性内置而非后加
- 从设计之初就考虑安全性
- 所有危险操作都有内置的安全检查
- 用户控制权始终是第一优先级

### 4. 可扩展性通过组合实现
- 通过动态组件注入实现扩展
- 支持用户自定义覆盖
- 保持核心简洁的同时支持个性化

这个实际提示词分析揭示了 Google 在 Gemini CLI 中采用的务实而高效的提示词工程方法，为我们提供了宝贵的实践参考。
