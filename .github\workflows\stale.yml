name: Mark stale issues and pull requests

# Run as a daily cron at 1:30 AM
on:
  schedule:
    - cron: '30 1 * * *'
  workflow_dispatch: {}

jobs:
  stale:
    runs-on: ubuntu-latest
    if: ${{ github.repository == 'google-gemini/gemini-cli' }}
    permissions:
      issues: write
      pull-requests: write
    concurrency:
      group: ${{ github.workflow }}-stale
      cancel-in-progress: true
    steps:
      - uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: >
            This issue has been automatically marked as stale due to 60 days of inactivity.
            It will be closed in 14 days if no further activity occurs.
          stale-pr-message: >
            This pull request has been automatically marked as stale due to 60 days of inactivity.
            It will be closed in 14 days if no further activity occurs.
          close-issue-message: >
            This issue has been closed due to 14 additional days of inactivity after being marked as stale.
            If you believe this is still relevant, feel free to comment or reopen the issue. Thank you!
          close-pr-message: >
            This pull request has been closed due to 14 additional days of inactivity after being marked as stale.
            If this is still relevant, you are welcome to reopen or leave a comment. Thanks for contributing!
          days-before-stale: 60
          days-before-close: 14
          exempt-issue-labels: pinned,security
          exempt-pr-labels: pinned,security
