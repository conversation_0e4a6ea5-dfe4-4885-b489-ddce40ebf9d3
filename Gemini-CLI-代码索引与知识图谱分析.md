# Gemini CLI 代码索引与知识图谱分析

## 🎯 核心发现

经过深入分析，我发现了一个重要的事实：**Gemini CLI 并没有实现传统意义上的代码索引和知识图谱系统**。相反，它采用了一种更加务实和高效的方法。

## 🔍 "无索引"的设计哲学

### 1. 为什么不使用传统索引？

#### A. 传统代码索引的问题
```typescript
// ❌ 传统索引系统的复杂性
interface TraditionalCodeIndex {
  symbols: Map<string, SymbolInfo[]>;
  dependencies: Map<string, string[]>;
  references: Map<string, Reference[]>;
  ast: Map<string, ASTNode>;
  
  // 需要维护的复杂状态
  buildIndex(): Promise<void>;
  updateIndex(filePath: string): Promise<void>;
  querySymbol(name: string): SymbolInfo[];
  findReferences(symbol: string): Reference[];
}
```

**传统索引的挑战**：
- **维护成本高**：文件变更时需要重新索引
- **内存占用大**：大型项目的索引可能占用大量内存
- **实时性差**：索引更新可能滞后于代码变更
- **复杂性高**：需要处理各种语言的语法差异

#### B. Gemini CLI 的替代方案
```typescript
// ✅ Gemini CLI 的实时搜索方案
class RealTimeCodeDiscovery {
  // 不维护持久化索引，而是实时搜索
  async findCode(query: string): Promise<SearchResult[]> {
    return Promise.all([
      this.globSearch(query),      // 文件名搜索
      this.grepSearch(query),      // 内容搜索
      this.structureSearch(query)  // 结构搜索
    ]);
  }
}
```

## 🏗️ 实际的"知识图谱"实现

### 1. 文件发现服务 (FileDiscoveryService)

```typescript
// packages/core/src/services/fileDiscoveryService.ts
export class FileDiscoveryService {
  private gitIgnoreFilter: GitIgnoreFilter | null = null;
  private geminiIgnoreFilter: GitIgnoreFilter | null = null;
  private projectRoot: string;

  constructor(projectRoot: string) {
    this.projectRoot = path.resolve(projectRoot);
    
    // 构建项目感知的过滤器
    if (isGitRepository(this.projectRoot)) {
      const parser = new GitIgnoreParser(this.projectRoot);
      parser.loadGitRepoPatterns();
      this.gitIgnoreFilter = parser;
    }
    
    // 加载 Gemini 特定的忽略规则
    const gParser = new GitIgnoreParser(this.projectRoot);
    gParser.loadPatterns('.geminiignore');
    this.geminiIgnoreFilter = gParser;
  }

  // 智能文件过滤
  filterFiles(filePaths: string[], options: FilterFilesOptions): string[] {
    return filePaths.filter((filePath) => {
      if (options.respectGitIgnore && this.shouldGitIgnoreFile(filePath)) {
        return false;
      }
      if (options.respectGeminiIgnore && this.shouldGeminiIgnoreFile(filePath)) {
        return false;
      }
      return true;
    });
  }
}
```

**设计智慧**：
- **项目感知**：理解 Git 仓库结构和忽略规则
- **智能过滤**：自动排除不相关的文件
- **轻量级**：不维护复杂的索引结构

### 2. 文件结构分析 (getFolderStructure)

```typescript
// packages/core/src/utils/getFolderStructure.ts
export async function getFolderStructure(
  directory: string,
  options?: FolderStructureOptions
): Promise<string> {
  
  const mergedOptions = {
    maxItems: options?.maxItems ?? 200,
    ignoredFolders: options?.ignoredFolders ?? new Set(['node_modules', '.git', 'dist']),
    fileIncludePattern: options?.fileIncludePattern,
    fileService: options?.fileService,
    fileFilteringOptions: options?.fileFilteringOptions ?? DEFAULT_FILE_FILTERING_OPTIONS
  };

  // 使用 BFS 算法遍历目录结构
  const structureRoot = await readFullStructure(directory, mergedOptions);
  
  // 生成树状结构字符串
  return formatStructureAsTree(structureRoot);
}

// BFS 遍历实现
async function readFullStructure(
  rootPath: string,
  options: MergedFolderStructureOptions
): Promise<FullFolderInfo | null> {
  
  const queue: Array<{ folderInfo: FullFolderInfo; currentPath: string }> = [
    { folderInfo: rootNode, currentPath: rootPath }
  ];
  
  let currentItemCount = 0;
  const processedPaths = new Set<string>(); // 避免循环引用
  
  while (queue.length > 0 && currentItemCount < options.maxItems) {
    const { folderInfo, currentPath } = queue.shift()!;
    
    // 避免重复处理
    if (processedPaths.has(currentPath)) continue;
    processedPaths.add(currentPath);
    
    // 读取目录内容
    const entries = await fs.readdir(currentPath, { withFileTypes: true });
    
    // 处理文件和子目录
    for (const entry of entries.sort((a, b) => a.name.localeCompare(b.name))) {
      if (currentItemCount >= options.maxItems) break;
      
      const itemPath = path.join(currentPath, entry.name);
      
      // 应用过滤规则
      if (options.fileService?.shouldIgnoreFile(itemPath)) continue;
      
      if (entry.isFile()) {
        folderInfo.files.push(entry.name);
        currentItemCount++;
      } else if (entry.isDirectory()) {
        const subFolder = createSubFolderInfo(entry.name, itemPath);
        folderInfo.subFolders.push(subFolder);
        queue.push({ folderInfo: subFolder, currentPath: itemPath });
        currentItemCount++;
      }
    }
  }
  
  return rootNode;
}
```

**核心特点**：
- **实时生成**：不预先构建索引，而是实时分析
- **智能限制**：限制遍历深度和项目数量，避免性能问题
- **过滤集成**：集成 Git 和 Gemini 的忽略规则
- **结构化输出**：生成易于理解的树状结构

### 3. 记忆系统作为"知识图谱"

```typescript
// packages/core/src/utils/memoryDiscovery.ts
export async function loadHierarchicalGeminiMemory(
  currentWorkingDirectory: string,
  debugMode: boolean,
  fileService: FileDiscoveryService,
  settings: MergedSettings,
  extensionContextFilePaths: string[] = [],
  importFormat: 'flat' | 'tree' = 'tree'
): Promise<{ memoryContent: string; fileCount: number }> {
  
  // 层次化搜索策略
  const searchPaths = [
    currentWorkingDirectory,  // 当前项目
    path.dirname(currentWorkingDirectory), // 父目录
    homedir(),               // 用户主目录
    ...extensionContextFilePaths // 扩展路径
  ];
  
  const allPaths = new Set<string>();
  
  // 在每个路径中搜索 GEMINI.md 文件
  for (const searchPath of searchPaths) {
    const geminiFiles = await bfsFileSearch(searchPath, {
      fileName: 'GEMINI.md',
      maxDirs: 200,
      fileService,
      fileFilteringOptions: settings.fileFilteringOptions
    });
    
    geminiFiles.forEach(file => allPaths.add(file));
  }
  
  // 读取和合并所有记忆文件
  return readAndCombineGeminiMdFiles(Array.from(allPaths), importFormat);
}
```

**记忆系统作为知识图谱**：
- **分层知识**：全局 → 项目 → 组件级别的知识
- **上下文感知**：根据当前位置加载相关知识
- **动态组合**：实时组合多个知识源
- **扩展支持**：支持第三方扩展提供的知识

## 🔧 实时搜索引擎

### 1. 三层搜索策略

```typescript
// 实时搜索的三个层次
class RealTimeSearchEngine {
  
  // 第1层：文件名搜索 (GlobTool)
  async searchByFileName(pattern: string): Promise<string[]> {
    return await glob(pattern, {
      cwd: this.projectRoot,
      ignore: ['**/node_modules/**', '**/.git/**'],
      nodir: true,
      stat: true,
      // 按修改时间排序，最新的在前
    });
  }
  
  // 第2层：内容搜索 (GrepTool)
  async searchByContent(pattern: string): Promise<GrepMatch[]> {
    // 优先级搜索策略
    try {
      // 1. 尝试 git grep (最快)
      if (isGitRepository(this.projectRoot)) {
        return await this.gitGrep(pattern);
      }
    } catch (error) {
      // 2. 回退到系统 grep
      try {
        return await this.systemGrep(pattern);
      } catch (error) {
        // 3. 最后使用 JavaScript 实现
        return await this.jsGrep(pattern);
      }
    }
  }
  
  // 第3层：结构搜索 (基于项目结构)
  async searchByStructure(query: string): Promise<StructureMatch[]> {
    const structure = await getFolderStructure(this.projectRoot);
    return this.analyzeStructureForQuery(structure, query);
  }
}
```

### 2. 智能搜索优化

```typescript
// 搜索性能优化策略
class SearchOptimizer {
  
  // 并行搜索
  async parallelSearch(query: string): Promise<SearchResult[]> {
    const [fileMatches, contentMatches, structureMatches] = await Promise.all([
      this.searchByFileName(`**/*${query}*`),
      this.searchByContent(query),
      this.searchByStructure(query)
    ]);
    
    return this.mergeAndRankResults(fileMatches, contentMatches, structureMatches);
  }
  
  // 结果排序和去重
  mergeAndRankResults(...results: SearchResult[][]): SearchResult[] {
    const allResults = results.flat();
    const uniqueResults = this.deduplicateResults(allResults);
    
    return uniqueResults.sort((a, b) => {
      // 排序策略：
      // 1. 文件名匹配优先
      // 2. 最近修改的文件优先
      // 3. 项目核心目录优先 (src/, lib/ 等)
      return this.calculateRelevanceScore(b) - this.calculateRelevanceScore(a);
    });
  }
  
  // 相关性评分
  calculateRelevanceScore(result: SearchResult): number {
    let score = 0;
    
    // 文件名匹配加分
    if (result.type === 'filename_match') score += 50;
    
    // 最近修改加分
    const daysSinceModified = (Date.now() - result.lastModified) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 20 - daysSinceModified);
    
    // 核心目录加分
    if (result.path.includes('/src/') || result.path.includes('/lib/')) score += 30;
    
    // 测试文件减分
    if (result.path.includes('.test.') || result.path.includes('.spec.')) score -= 10;
    
    return score;
  }
}
```

## 🧠 AI 驱动的"语义索引"

### 1. 上下文理解机制

```typescript
// AI 如何理解项目上下文
class ProjectContextAnalyzer {
  
  async analyzeProject(projectRoot: string): Promise<ProjectContext> {
    // 1. 技术栈检测
    const techStack = await this.detectTechStack(projectRoot);
    
    // 2. 项目结构分析
    const structure = await getFolderStructure(projectRoot);
    
    // 3. 配置文件分析
    const configs = await this.analyzeConfigFiles(projectRoot);
    
    // 4. 依赖关系分析
    const dependencies = await this.analyzeDependencies(projectRoot);
    
    return {
      techStack,
      structure,
      configs,
      dependencies,
      conventions: this.extractConventions(structure, configs)
    };
  }
  
  // 技术栈检测
  async detectTechStack(projectRoot: string): Promise<TechStack> {
    const packageJson = await this.readPackageJson(projectRoot);
    const configFiles = await this.findConfigFiles(projectRoot);
    
    return {
      language: this.detectLanguage(packageJson, configFiles),
      framework: this.detectFramework(packageJson),
      buildTool: this.detectBuildTool(configFiles),
      testFramework: this.detectTestFramework(packageJson)
    };
  }
  
  // 项目约定提取
  extractConventions(structure: string, configs: ConfigFiles): ProjectConventions {
    return {
      namingConvention: this.analyzeNamingConvention(structure),
      directoryStructure: this.analyzeDirectoryStructure(structure),
      codeStyle: this.analyzeCodeStyle(configs),
      importStyle: this.analyzeImportStyle(configs)
    };
  }
}
```

### 2. 动态知识构建

```typescript
// 实时构建项目知识
class DynamicKnowledgeBuilder {
  
  async buildKnowledge(query: string, projectRoot: string): Promise<ProjectKnowledge> {
    // 1. 基于查询的相关文件发现
    const relevantFiles = await this.findRelevantFiles(query, projectRoot);
    
    // 2. 文件内容分析
    const fileAnalysis = await this.analyzeFiles(relevantFiles);
    
    // 3. 关系图构建
    const relationships = await this.buildRelationships(fileAnalysis);
    
    // 4. 知识图谱生成
    return {
      files: fileAnalysis,
      relationships,
      context: await this.buildContext(query, fileAnalysis),
      suggestions: await this.generateSuggestions(query, relationships)
    };
  }
  
  // 关系分析
  async buildRelationships(files: FileAnalysis[]): Promise<Relationship[]> {
    const relationships: Relationship[] = [];
    
    for (const file of files) {
      // 导入关系
      for (const importPath of file.imports) {
        relationships.push({
          type: 'imports',
          from: file.path,
          to: this.resolveImportPath(importPath, file.path),
          strength: 1.0
        });
      }
      
      // 函数调用关系
      for (const call of file.functionCalls) {
        relationships.push({
          type: 'calls',
          from: file.path,
          to: call.target,
          strength: 0.8
        });
      }
      
      // 类型依赖关系
      for (const type of file.typeReferences) {
        relationships.push({
          type: 'uses_type',
          from: file.path,
          to: type.definition,
          strength: 0.6
        });
      }
    }
    
    return relationships;
  }
}
```

## 💡 设计哲学的深度解析

### 1. 为什么选择"无索引"设计？

#### A. 实时性优势
```typescript
// ✅ 实时搜索的优势
const realTimeAdvantages = {
  freshness: "总是反映最新的代码状态",
  simplicity: "无需维护复杂的索引状态",
  flexibility: "可以处理任意类型的查询",
  scalability: "搜索性能与项目大小线性相关"
};

// ❌ 传统索引的问题
const indexingProblems = {
  staleness: "索引可能过时",
  complexity: "需要处理增量更新",
  memory: "大型项目索引占用大量内存",
  maintenance: "需要处理索引损坏和重建"
};
```

#### B. AI 时代的新思路
```typescript
// AI 改变了代码理解的方式
class AICodeUnderstanding {
  
  // 传统方式：预先构建符号表
  traditionalApproach() {
    // 1. 解析所有文件的 AST
    // 2. 提取符号和引用
    // 3. 构建索引数据库
    // 4. 维护索引一致性
  }
  
  // AI 方式：实时理解和推理
  aiApproach() {
    // 1. 基于查询实时搜索相关文件
    // 2. 将文件内容发送给 AI
    // 3. AI 理解代码语义和关系
    // 4. 生成智能的修改建议
  }
}
```

### 2. 分层知识系统

```typescript
// Gemini CLI 的知识层次
const knowledgeHierarchy = {
  // 第1层：文件系统知识
  filesystem: {
    source: "实时文件扫描",
    content: "文件结构、命名约定、目录组织",
    update: "实时"
  },
  
  // 第2层：项目配置知识
  configuration: {
    source: "配置文件分析",
    content: "技术栈、构建工具、依赖关系",
    update: "按需"
  },
  
  // 第3层：用户记忆知识
  memory: {
    source: "GEMINI.md 文件",
    content: "用户偏好、项目约定、历史决策",
    update: "用户驱动"
  },
  
  // 第4层：AI 推理知识
  reasoning: {
    source: "AI 实时分析",
    content: "代码语义、关系推理、修改建议",
    update: "每次查询"
  }
};
```

### 3. 性能优化策略

```typescript
// 搜索性能优化
class PerformanceOptimization {
  
  // 1. 智能缓存
  private searchCache = new Map<string, SearchResult[]>();
  
  async search(query: string): Promise<SearchResult[]> {
    // 缓存最近的搜索结果
    if (this.searchCache.has(query)) {
      return this.searchCache.get(query)!;
    }
    
    const results = await this.performSearch(query);
    this.searchCache.set(query, results);
    
    // 限制缓存大小
    if (this.searchCache.size > 100) {
      const firstKey = this.searchCache.keys().next().value;
      this.searchCache.delete(firstKey);
    }
    
    return results;
  }
  
  // 2. 搜索范围限制
  async limitedSearch(query: string): Promise<SearchResult[]> {
    return await this.performSearch(query, {
      maxFiles: 1000,        // 最多搜索 1000 个文件
      maxDepth: 10,          // 最大目录深度
      timeoutMs: 5000,       // 5 秒超时
      excludePatterns: [     // 排除模式
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**'
      ]
    });
  }
  
  // 3. 并行搜索优化
  async parallelOptimizedSearch(query: string): Promise<SearchResult[]> {
    // 使用 Worker 线程进行并行搜索
    const workers = [
      this.createSearchWorker('filename'),
      this.createSearchWorker('content'),
      this.createSearchWorker('structure')
    ];
    
    const results = await Promise.all(
      workers.map(worker => worker.search(query))
    );
    
    return this.mergeResults(results);
  }
}
```

## 🚀 核心洞察总结

### 1. **"无索引"不等于"无知识"**
- Gemini CLI 通过实时搜索和 AI 推理构建动态知识
- 避免了传统索引的维护成本和一致性问题
- 利用 AI 的语义理解能力弥补了索引的不足

### 2. **分层知识系统**
- **文件系统层**：实时的文件结构和内容
- **配置层**：项目的技术栈和约定
- **记忆层**：用户的偏好和历史决策
- **推理层**：AI 的实时分析和建议

### 3. **性能与准确性的平衡**
- 通过智能搜索限制和缓存优化性能
- 利用并行搜索提高响应速度
- 通过相关性排序提高结果质量

### 4. **AI 原生的设计思路**
- 不是为 AI 添加索引，而是让 AI 成为索引
- 实时理解胜过预先构建
- 语义推理胜过符号匹配

这种设计展现了 Google 在 AI 时代对代码工具的全新思考：**与其维护复杂的静态索引，不如利用 AI 的实时理解能力构建动态的代码知识系统**。

## 🔬 实际实现细节深度分析

### 1. BFS 文件搜索算法

```typescript
// packages/core/src/utils/bfsFileSearch.ts
export async function bfsFileSearch(
  startDir: string,
  options: BfsFileSearchOptions
): Promise<string[]> {

  const { fileName, maxDirs = 200, fileService, fileFilteringOptions } = options;
  const foundFiles: string[] = [];
  const visitedDirs = new Set<string>();
  const queue: string[] = [startDir];
  let dirsProcessed = 0;

  while (queue.length > 0 && dirsProcessed < maxDirs) {
    const currentDir = queue.shift()!;

    // 避免重复访问和循环引用
    const resolvedDir = path.resolve(currentDir);
    if (visitedDirs.has(resolvedDir)) continue;
    visitedDirs.add(resolvedDir);

    try {
      const entries = await fs.readdir(currentDir, { withFileTypes: true });
      dirsProcessed++;

      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);

        // 应用文件过滤规则
        if (fileService?.shouldIgnoreFile(fullPath, fileFilteringOptions)) {
          continue;
        }

        if (entry.isFile() && entry.name === fileName) {
          foundFiles.push(fullPath);
        } else if (entry.isDirectory()) {
          // 避免搜索常见的大型目录
          if (!IGNORED_DIRS.has(entry.name)) {
            queue.push(fullPath);
          }
        }
      }
    } catch (error) {
      // 忽略权限错误，继续搜索其他目录
      if (options.debug) {
        console.warn(`Cannot read directory ${currentDir}: ${error.message}`);
      }
    }
  }

  return foundFiles.sort(); // 返回排序后的结果
}
```

**算法特点**：
- **广度优先**：确保先搜索浅层目录
- **循环检测**：避免符号链接导致的无限循环
- **性能限制**：限制搜索的目录数量
- **错误容忍**：权限错误不会中断整个搜索

### 2. Git 集成的智能过滤

```typescript
// packages/core/src/utils/gitIgnoreParser.ts
export class GitIgnoreParser implements GitIgnoreFilter {
  private patterns: string[] = [];
  private ignoreInstance: Ignore | null = null;

  constructor(private projectRoot: string) {}

  loadGitRepoPatterns(): void {
    // 加载多个 .gitignore 文件
    const gitignoreFiles = [
      path.join(this.projectRoot, '.gitignore'),
      path.join(this.projectRoot, '.git', 'info', 'exclude'),
      // 全局 gitignore
      this.getGlobalGitignorePath()
    ];

    for (const file of gitignoreFiles) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        this.patterns.push(...this.parseGitignoreContent(content));
      } catch (error) {
        // 文件不存在时忽略
      }
    }

    // 使用 ignore 库构建过滤器
    this.ignoreInstance = ignore().add(this.patterns);
  }

  isIgnored(filePath: string): boolean {
    if (!this.ignoreInstance) return false;

    // 转换为相对于项目根目录的路径
    const relativePath = path.relative(this.projectRoot, filePath);
    return this.ignoreInstance.ignores(relativePath);
  }

  // 解析 .gitignore 内容
  private parseGitignoreContent(content: string): string[] {
    return content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#')) // 移除注释和空行
      .map(line => this.normalizePattern(line));
  }
}
```

**智能过滤特性**：
- **多源合并**：合并项目、全局和 Git 内部的忽略规则
- **标准兼容**：完全兼容 Git 的忽略语法
- **性能优化**：使用高效的模式匹配库
- **路径规范化**：正确处理相对路径和绝对路径

### 3. 工具注册表的动态发现

```typescript
// packages/core/src/tools/tool-registry.ts
export class ToolRegistry {
  private tools: Map<string, Tool> = new Map();
  private mcpTools: Map<string, DiscoveredMCPTool> = new Map();

  async discoverAllTools(): Promise<void> {
    // 1. 注册内置工具
    await this.registerBuiltinTools();

    // 2. 发现 MCP 工具
    await this.discoverMcpTools();

    // 3. 发现外部工具
    await this.discoverExternalTools();
  }

  // MCP 工具发现
  async discoverMcpTools(): Promise<void> {
    const mcpServers = this.config.getMcpServers();

    for (const [serverName, serverConfig] of Object.entries(mcpServers)) {
      try {
        const discoveredTools = await discoverMcpTools(serverName, serverConfig);

        for (const tool of discoveredTools) {
          const mcpTool = new DiscoveredMCPTool(tool, serverName);
          this.mcpTools.set(tool.name, mcpTool);
          this.tools.set(tool.name, mcpTool);
        }
      } catch (error) {
        console.warn(`Failed to discover MCP tools from ${serverName}: ${error.message}`);
      }
    }
  }

  // 外部工具发现
  async discoverExternalTools(): Promise<void> {
    const discoveryCommands = this.config.getToolDiscoveryCommands();

    for (const command of discoveryCommands) {
      try {
        const tools = await this.executeDiscoveryCommand(command);

        for (const toolDef of tools) {
          const externalTool = new ExternalTool(toolDef, command);
          this.tools.set(toolDef.name, externalTool);
        }
      } catch (error) {
        console.warn(`Tool discovery command failed: ${command}: ${error.message}`);
      }
    }
  }

  // 执行工具发现命令
  private async executeDiscoveryCommand(command: string): Promise<ToolDefinition[]> {
    return new Promise((resolve, reject) => {
      const child = spawn('sh', ['-c', command], {
        stdio: ['ignore', 'pipe', 'pipe'],
        timeout: 10000 // 10 秒超时
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          try {
            const tools = JSON.parse(stdout.trim());
            resolve(Array.isArray(tools) ? tools : [tools]);
          } catch (error) {
            reject(new Error(`Invalid JSON output: ${error.message}`));
          }
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });
    });
  }
}
```

**动态发现特性**：
- **多源工具**：内置、MCP、外部工具的统一管理
- **容错机制**：单个工具发现失败不影响其他工具
- **超时保护**：防止工具发现命令无限等待
- **标准化接口**：所有工具都实现统一的接口

### 4. 内存导入处理器

```typescript
// packages/core/src/utils/memoryImportProcessor.ts
export async function processImports(
  content: string,
  basePath: string,
  fileService: FileDiscoveryService,
  importFormat: 'flat' | 'tree' = 'tree'
): Promise<string> {

  // 使用 marked 解析 Markdown
  const tokens = marked.lexer(content);
  const imports: ImportInfo[] = [];

  // 遍历所有 token 查找 @import 语法
  for (const token of tokens) {
    if (token.type === 'paragraph' || token.type === 'text') {
      const importMatches = token.raw.match(/@([^\s]+\.md)/g);

      if (importMatches) {
        for (const match of importMatches) {
          const importPath = match.substring(1); // 移除 @
          const resolvedPath = path.resolve(basePath, importPath);

          imports.push({
            originalText: match,
            importPath,
            resolvedPath,
            position: content.indexOf(match)
          });
        }
      }
    }
  }

  // 处理导入
  let processedContent = content;

  for (const importInfo of imports.reverse()) { // 从后往前处理，避免位置偏移
    try {
      const importedContent = await fs.readFile(importInfo.resolvedPath, 'utf-8');

      // 递归处理嵌套导入
      const nestedProcessed = await processImports(
        importedContent,
        path.dirname(importInfo.resolvedPath),
        fileService,
        importFormat
      );

      // 格式化导入内容
      const formattedContent = formatImportedContent(
        nestedProcessed,
        importInfo.importPath,
        importFormat
      );

      // 替换导入语句
      processedContent = processedContent.replace(
        importInfo.originalText,
        formattedContent
      );

    } catch (error) {
      console.warn(`Failed to import ${importInfo.importPath}: ${error.message}`);
      // 保留原始导入语句作为错误标记
    }
  }

  return processedContent;
}

// 格式化导入内容
function formatImportedContent(
  content: string,
  importPath: string,
  format: 'flat' | 'tree'
): string {

  if (format === 'flat') {
    return `\n<!-- Imported from ${importPath} -->\n${content}\n`;
  } else {
    // tree 格式：添加层次结构
    const indentedContent = content
      .split('\n')
      .map(line => line ? `  ${line}` : line)
      .join('\n');

    return `\n## 📁 ${importPath}\n${indentedContent}\n`;
  }
}
```

**导入处理特性**：
- **递归导入**：支持嵌套的 @import 语句
- **循环检测**：防止循环导入导致无限递归
- **格式化选项**：支持平铺和树状两种格式
- **错误处理**：导入失败时保留原始语句

### 5. 实时项目分析

```typescript
// 项目上下文的实时分析
class ProjectAnalyzer {

  async analyzeProjectInRealTime(projectRoot: string): Promise<ProjectInsights> {
    // 并行分析多个维度
    const [
      techStack,
      fileStructure,
      dependencies,
      gitInfo,
      codeMetrics
    ] = await Promise.all([
      this.analyzeTechStack(projectRoot),
      this.analyzeFileStructure(projectRoot),
      this.analyzeDependencies(projectRoot),
      this.analyzeGitInfo(projectRoot),
      this.analyzeCodeMetrics(projectRoot)
    ]);

    return {
      techStack,
      fileStructure,
      dependencies,
      gitInfo,
      codeMetrics,
      recommendations: this.generateRecommendations({
        techStack,
        fileStructure,
        dependencies
      })
    };
  }

  // 技术栈分析
  async analyzeTechStack(projectRoot: string): Promise<TechStackInfo> {
    const packageJsonPath = path.join(projectRoot, 'package.json');
    const configFiles = await this.findConfigFiles(projectRoot);

    let packageJson: any = {};
    try {
      packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
    } catch (error) {
      // package.json 不存在或无效
    }

    return {
      language: this.detectLanguage(configFiles),
      framework: this.detectFramework(packageJson.dependencies || {}),
      buildTool: this.detectBuildTool(configFiles),
      testFramework: this.detectTestFramework(packageJson.devDependencies || {}),
      packageManager: this.detectPackageManager(projectRoot),
      typescript: this.hasTypeScript(configFiles, packageJson)
    };
  }

  // 代码度量分析
  async analyzeCodeMetrics(projectRoot: string): Promise<CodeMetrics> {
    const sourceFiles = await glob('**/*.{ts,js,tsx,jsx}', {
      cwd: projectRoot,
      ignore: ['**/node_modules/**', '**/dist/**', '**/.git/**']
    });

    let totalLines = 0;
    let totalFiles = sourceFiles.length;
    let testFiles = 0;
    const languageDistribution: Record<string, number> = {};

    for (const file of sourceFiles) {
      const filePath = path.join(projectRoot, file);
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n').length;

      totalLines += lines;

      // 统计语言分布
      const ext = path.extname(file);
      languageDistribution[ext] = (languageDistribution[ext] || 0) + lines;

      // 统计测试文件
      if (file.includes('.test.') || file.includes('.spec.')) {
        testFiles++;
      }
    }

    return {
      totalLines,
      totalFiles,
      testFiles,
      testCoverage: testFiles / totalFiles,
      languageDistribution,
      averageFileSize: totalLines / totalFiles
    };
  }

  // 生成智能建议
  generateRecommendations(analysis: Partial<ProjectInsights>): string[] {
    const recommendations: string[] = [];

    // 基于技术栈的建议
    if (analysis.techStack?.framework === 'react' && !analysis.techStack.typescript) {
      recommendations.push("考虑迁移到 TypeScript 以获得更好的类型安全");
    }

    // 基于代码度量的建议
    if (analysis.codeMetrics?.testCoverage && analysis.codeMetrics.testCoverage < 0.3) {
      recommendations.push("测试覆盖率较低，建议增加更多测试");
    }

    // 基于文件结构的建议
    if (analysis.fileStructure?.hasConfigFiles && !analysis.fileStructure.hasDocumentation) {
      recommendations.push("项目缺少文档，建议添加 README.md");
    }

    return recommendations;
  }
}
```

**实时分析特性**：
- **并行分析**：同时分析多个维度，提高效率
- **智能推理**：基于分析结果生成改进建议
- **容错处理**：单个分析失败不影响其他分析
- **增量更新**：支持基于文件变更的增量分析

## 🎯 核心设计原理总结

### 1. **实时胜过预构建**
- 避免了索引维护的复杂性
- 始终反映最新的代码状态
- 利用现代硬件的搜索性能

### 2. **AI 驱动的语义理解**
- 不依赖语法解析，而是语义理解
- 能够处理不完整或有错误的代码
- 支持自然语言查询

### 3. **分层知识架构**
- 文件系统 → 配置 → 记忆 → AI 推理
- 每层都有不同的更新频率和可靠性
- 支持知识的组合和覆盖

### 4. **性能与准确性的平衡**
- 通过智能限制控制搜索范围
- 使用缓存和并行优化性能
- 基于相关性排序提高结果质量

这种"无索引"的设计代表了 AI 时代代码工具的新范式：**让 AI 成为动态的、智能的代码索引系统**。
