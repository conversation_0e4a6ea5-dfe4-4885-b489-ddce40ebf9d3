{"name": "@google/gemini-cli", "version": "0.1.15", "engines": {"node": ">=20.0.0"}, "type": "module", "workspaces": ["packages/*"], "private": "true", "repository": {"type": "git", "url": "git+https://github.com/google-gemini/gemini-cli.git"}, "config": {"sandboxImageUri": "us-docker.pkg.dev/gemini-code-dev/gemini-cli/sandbox:0.1.15"}, "scripts": {"start": "node scripts/start.js", "debug": "cross-env DEBUG=1 node --inspect-brk scripts/start.js", "auth:npm": "npx google-artifactregistry-auth", "auth:docker": "gcloud auth configure-docker us-west1-docker.pkg.dev", "auth": "npm run auth:npm && npm run auth:docker", "generate": "node scripts/generate-git-commit-info.js", "build": "node scripts/build.js", "build:vscode": "node scripts/build_vscode_companion.js", "build:all": "npm run build && npm run build:sandbox && npm run build:vscode", "build:packages": "npm run build --workspaces", "build:sandbox": "node scripts/build_sandbox.js --skip-npm-install-build", "bundle": "npm run generate && node esbuild.config.js && node scripts/copy_bundle_assets.js", "test": "npm run test --workspaces", "test:ci": "npm run test:ci --workspaces --if-present && npm run test:scripts", "test:scripts": "vitest run --config ./scripts/tests/vitest.config.ts", "test:e2e": "npm run test:integration:sandbox:none -- --verbose --keep-output", "test:integration:all": "npm run test:integration:sandbox:none && npm run test:integration:sandbox:docker && npm run test:integration:sandbox:podman", "test:integration:sandbox:none": "GEMINI_SANDBOX=false node integration-tests/run-tests.js", "test:integration:sandbox:docker": "GEMINI_SANDBOX=docker node integration-tests/run-tests.js", "test:integration:sandbox:podman": "GEMINI_SANDBOX=podman node integration-tests/run-tests.js", "lint": "eslint . --ext .ts,.tsx && eslint integration-tests", "lint:fix": "eslint . --fix && eslint integration-tests --fix", "lint:ci": "eslint . --ext .ts,.tsx --max-warnings 0 && eslint integration-tests --max-warnings 0", "format": "prettier --write .", "typecheck": "npm run typecheck --workspaces --if-present", "preflight": "npm run clean && npm ci && npm run format && npm run lint:ci && npm run build && npm run typecheck && npm run test:ci", "prepare": "npm run bundle", "prepare:package": "node scripts/prepare-package.js", "release:version": "node scripts/version.js", "telemetry": "node scripts/telemetry.js", "clean": "node scripts/clean.js"}, "bin": {"gemini": "bundle/gemini.js"}, "files": ["bundle/", "README.md", "LICENSE"], "devDependencies": {"@types/marked": "^5.0.2", "@types/micromatch": "^4.0.9", "@types/mime-types": "^3.0.1", "@types/minimatch": "^5.1.2", "@types/mock-fs": "^4.13.4", "@types/shell-quote": "^1.7.5", "@vitest/coverage-v8": "^3.1.1", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "esbuild": "^0.25.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-license-header": "^0.8.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "glob": "^10.4.5", "globals": "^16.0.0", "json": "^11.0.0", "lodash": "^4.17.21", "memfs": "^4.17.2", "mock-fs": "^5.5.0", "prettier": "^3.5.3", "react-devtools-core": "^4.28.5", "typescript-eslint": "^8.30.1", "vitest": "^3.2.4", "yargs": "^17.7.2"}}